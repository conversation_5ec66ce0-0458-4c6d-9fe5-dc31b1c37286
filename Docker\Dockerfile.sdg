FROM ubuntu:22.04

ENV DEBIAN_FRONTEND=noninteractive

# Base dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    libltdl7 libxml2 libtbb2 libtbb-dev \
    systemd systemd-sysv procps fuse3 libfuse2 \
    nano curl wget openssl netcat-openbsd \
    && apt-get clean && rm -rf /var/lib/apt/lists/*

# Directories (include /run/systemd/system so aksusbd preinst checks pass)
RUN mkdir -p /home/<USER>/opt/sdg/data /var/hasplm /etc/tmw/sdg/ssl/certs /etc/tmw/sdg/ssl/private /run/systemd/system \
 && echo user_allow_other >> /etc/fuse.conf

# Payload
COPY ./assets/aksusbd_8.53-1_amd64.deb /home/<USER>/
COPY ./assets/haspvlib_x86_64_102099.so /var/hasplm/
# Also stage a copy outside /var/hasplm so we can re-populate tmpfs at runtime
RUN mkdir -p /usr/local/share/hasp/init && cp /var/hasplm/haspvlib_x86_64_102099.so /usr/local/share/hasp/
RUN [ -d /var/hasplm/init ] && cp -r /var/hasplm/init/* /usr/local/share/hasp/init/ || true
COPY ./assets/install_v2c_x86_64 /home/<USER>/
COPY ./assets/SDG_Unlocked_20190321_171527.v2c /home/<USER>/
COPY ./assets/tmwsdg-5.2.3-22411.x86_64_U22.04.deb /home/<USER>/

RUN chmod +x /var/hasplm/haspvlib_x86_64_102099.so && chmod +x /home/<USER>/install_v2c_x86_64

# Prevent services from auto-starting during image build (no systemd at build-time)
RUN printf '#!/bin/sh\nexit 101\n' > /usr/sbin/policy-rc.d && chmod +x /usr/sbin/policy-rc.d
# Stub systemctl during build to satisfy postinst scripts
RUN if [ -x /bin/systemctl ]; then cp /bin/systemctl /bin/systemctl.real && ln -sf /bin/true /bin/systemctl; fi; \
    if [ -x /usr/bin/systemctl ]; then cp /usr/bin/systemctl /usr/bin/systemctl.real && ln -sf /bin/true /usr/bin/systemctl; fi
# Install licensing and SDG packages
RUN dpkg -i /home/<USER>/aksusbd_8.53-1_amd64.deb || apt-get install -f -y
RUN dpkg -i /home/<USER>/tmwsdg-5.2.3-22411.x86_64_U22.04.deb || apt-get install -f -y
# Restore systemctl and default service policy for runtime
RUN if [ -f /bin/systemctl.real ]; then mv -f /bin/systemctl.real /bin/systemctl; fi; \
    if [ -f /usr/bin/systemctl.real ]; then mv -f /usr/bin/systemctl.real /usr/bin/systemctl; fi; \
    rm -f /usr/sbin/policy-rc.d

# Start script implementing Option 2 (in-container licensing)
COPY start-sdg.sh /usr/local/bin/start-sdg.sh
COPY restart-gtw.sh /usr/local/bin/restart-gtw.sh
COPY supervisor-gtw.sh /usr/local/bin/supervisor-gtw.sh
COPY reset-setup.sh /usr/local/bin/reset-setup.sh
COPY start-supervision.sh /usr/local/bin/start-supervision.sh
COPY configure-supervisor.sh /usr/local/bin/configure-supervisor.sh
RUN chmod +x /usr/local/bin/start-sdg.sh /usr/local/bin/restart-gtw.sh /usr/local/bin/supervisor-gtw.sh /usr/local/bin/reset-setup.sh /usr/local/bin/start-supervision.sh /usr/local/bin/configure-supervisor.sh

EXPOSE 58090 58080 4885 102 2404 2000 502 1947
STOPSIGNAL SIGTERM
CMD ["/usr/local/bin/start-sdg.sh"]

