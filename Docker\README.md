# SDG Docker Container - Production Ready

This Docker setup provides a complete, enterprise-grade SDG (SCADA Data Gateway) environment with advanced supervision, persistent storage, intelligent license management, and self-healing capabilities.

## 🚀 Key Features

- **🔄 Self-Healing Architecture** - Automatic recovery from service failures
- **💾 Complete Persistence** - Licenses, configurations, and data survive container restarts
- **🛡️ Smart License Management** - Preserves user-installed permanent licenses
- **📊 Advanced Supervision** - Multi-layered monitoring with configurable auto-restart
- **🔧 Zero-Touch Operation** - Fully automated setup and maintenance
- **🌐 Production Ready** - Enterprise-grade robustness and reliability

## 🏗️ Architecture

### Container Design

- **Single Container**: Simplified deployment with all components integrated
- **Persistent Volumes**: Critical data survives container lifecycle
- **Multi-Layer Supervision**: Built-in + External + Watchdog monitoring
- **Graceful Shutdown**: Proper cleanup and signal handling

### Storage Architecture

```
Persistent Volumes:
├── sdg_data:/opt/sdg/data          # Application data & supervisor config
├── sdg_config:/etc/tmw/sdg         # GTW config, SSL certs, workspaces
├── hasplm_data:/var/hasplm         # Sentinel license files
└── hasplm_config:/etc/hasplm       # Sentinel configuration
```

### Supervision Layers

1. **Main Process (PID 1)** - Robust start-sdg.sh with health monitoring
2. **External Supervisor** - Configurable GTW service monitoring
3. **Supervisor Watchdog** - Monitors and restarts supervisor if needed
4. **License Change Detection** - Resets restart counts when licenses change

## 📋 Prerequisites

### System Requirements

- **Docker Engine** 20.10+ with Docker Compose V2
- **Platform**: Linux/amd64 (WSL2 recommended for Windows)
- **Memory**: 2GB+ available RAM
- **Storage**: 5GB+ available disk space

### Required Assets

Ensure all files are present in the `assets/` directory:

```text
assets/
├── tmwsdg-5.2.3-22411.x86_64_U22.04.deb    # SDG application package
├── aksusbd_8.53-1_amd64.deb                # Sentinel license daemon
├── haspvlib_x86_64_102099.so               # Sentinel library
├── install_v2c_x86_64                      # V2C license installer
└── SDG_Unlocked_20190321_171527.v2c        # Bootstrap license file
```

### License Requirements

- **Bootstrap License**: Included V2C file enables licensing runtime operation
- **Permanent License**: Required for full operation - install via web interface
- **Container Compatibility**: Perpetual licenses work best (non-concurrent)

## 🚀 Quick Start

### Step 1: Build and Start

```bash
# Clone/navigate to the Docker directory
cd Docker

# Build and start the container
docker-compose up -d --build
```

### Step 2: Monitor Startup

```bash
# Watch the startup process
docker-compose logs -f

# Check service status
docker exec sdg-application /usr/local/bin/reset-setup.sh status
```

### Step 3: Access Web Interface

- **URL**: <https://127.0.0.1:58090>
- **Login**: admin / passwordA1.
- **Note**: Accept the self-signed SSL certificate

### Step 4: Install Permanent License (Required)

1. Access the web interface
2. Navigate to licensing section
3. Upload your permanent license file
4. Container will automatically preserve the license across restarts

**Note**: The included bootstrap license only enables basic operation. A permanent license is required for full SDG functionality.

## 🔧 Container Management

### Basic Operations

```bash
# Start container
docker-compose up -d

# Stop container
docker-compose down

# Restart container
docker-compose restart

# View logs
docker-compose logs -f

# Access container shell
docker-compose exec sdg bash
```

### Advanced Operations

```bash
# Rebuild from scratch
docker-compose down -v  # Remove volumes
docker-compose build --no-cache
docker-compose up -d

# Check service status
docker exec sdg-application /usr/local/bin/supervisor-gtw.sh status

# Monitor supervisor logs
docker exec sdg-application tail -f /var/log/gtw-supervisor.log
```

## 🛠️ Advanced Management

### Supervision Control

```bash
# Check supervisor status
docker exec sdg-application /usr/local/bin/supervisor-gtw.sh status

# Configure supervisor settings
docker exec sdg-application /usr/local/bin/configure-supervisor.sh

# Available presets: fast, normal, conservative
docker exec sdg-application /usr/local/bin/configure-supervisor.sh normal

# Manual service restart
docker exec sdg-application /usr/local/bin/restart-gtw.sh

# Start supervision (if stopped)
docker exec sdg-application /usr/local/bin/start-supervision.sh
```

### License Management

```bash
# Check license status
docker exec sdg-application /usr/local/bin/reset-setup.sh status

# Force bootstrap license reinstall (overwrites user license!)
docker exec sdg-application /usr/local/bin/reset-setup.sh force-license

# Reset license state (allows fresh bootstrap install)
docker exec sdg-application /usr/local/bin/reset-setup.sh reset-license

# Force configuration reset
docker exec sdg-application /usr/local/bin/reset-setup.sh force-config
```

### Monitoring & Diagnostics

```bash
# View all logs
docker exec sdg-application tail -f /var/log/start-sdg.log
docker exec sdg-application tail -f /var/log/gtw-supervisor.log
docker exec sdg-application tail -f /var/log/gtw-monitor.log
docker exec sdg-application tail -f /var/log/gtw-engine.log

# Check running processes
docker exec sdg-application pgrep -af GTW
docker exec sdg-application pgrep -af hasplmd
docker exec sdg-application pgrep -af supervisor

# Check license files
docker exec sdg-application find /var/hasplm/installed -type f
```

## 🌐 Network Configuration

### Port Mappings

| Port | Protocol | Service | Description |
|------|----------|---------|-------------|
| 58090 | HTTPS | Web Interface | SDG Web Monitor (Primary UI) |
| 58080 | HTTP | Engine API | SDG Engine REST API |
| 4885 | TCP | OPC UA | OPC UA Server |
| 1947 | TCP | License Manager | HASP/Sentinel License Manager |
| 102-103 | TCP | IEC 61850 | IEC 61850 MMS |
| 2404-2406 | TCP | IEC 104 | IEC 60870-5-104 |
| 20000-20003 | TCP | DNP3 | DNP3 Outstation/Master |
| 502-503 | TCP | Modbus | Modbus TCP |

### Default Configuration

- **Web Interface**: <https://127.0.0.1:58090>
- **Admin Credentials**: admin / passwordA1.
- **Internal Communication**: All GTW services use localhost (127.0.0.1)
- **SSL Certificates**: Auto-generated self-signed certificates

## ⚙️ Configuration Management

### Supervisor Configuration

The container includes configurable supervision with three presets:

| Preset | Health Check | Restart Delay | Max Restarts | Use Case |
|--------|-------------|---------------|--------------|----------|
| **fast** | 9s | 9s | 30 | Quick recovery, stable services |
| **normal** | 15s | 15s | 15 | Balanced performance (default) |
| **conservative** | 45s | 30s | 9 | Unstable services, avoid restart storms |

```bash
# Configure supervision
docker exec sdg-application /usr/local/bin/configure-supervisor.sh normal
```

### License Management Features

- **Smart Bootstrap Installation**: Only installs bootstrap license on first run
- **Permanent License Preservation**: User-installed licenses survive restarts
- **Adaptive Restart Management**: Intelligently manages service restart attempts
- **Persistent Storage**: All license data stored in persistent volumes

## 🔧 Troubleshooting

### Quick Diagnostics

Use the built-in status command for comprehensive diagnostics:

```bash
docker exec sdg-application /usr/local/bin/reset-setup.sh status
```

This shows:

- License status and files
- SSL certificate configuration
- GTW configuration status
- Bootstrap license installation policy

### Service Issues

#### GTW Services Not Running

```bash
# Check supervisor status
docker exec sdg-application /usr/local/bin/supervisor-gtw.sh status

# Check if supervisor is running
docker exec sdg-application pgrep -f supervisor-gtw

# Restart supervision if needed
docker exec sdg-application /usr/local/bin/start-supervision.sh

# Manual service restart
docker exec sdg-application /usr/local/bin/restart-gtw.sh
```

#### Supervisor Stopped

The container includes automatic supervisor recovery, but you can manually restart:

```bash
# Check if watchdog is running
docker exec sdg-application ps aux | grep "sleep 300"

# Manual supervisor restart
docker exec sdg-application /usr/local/bin/start-supervision.sh
```

### License Issues

#### License Not Functional

```bash
# Check license files
docker exec sdg-application find /var/hasplm/installed -type f

# Check license logs
docker exec sdg-application find /etc/tmw/sdg/LicenseLogs -name "*.log" -exec tail {} \;

# Force bootstrap license reinstall (WARNING: overwrites user license)
docker exec sdg-application /usr/local/bin/reset-setup.sh force-license
```

#### Permanent License Lost

If your permanent license was lost after restart:

```bash
# Check if license files exist
docker exec sdg-application /usr/local/bin/reset-setup.sh status

# If files missing, reinstall via web interface
# The container will preserve it in persistent storage
```

### SSL Certificate Issues

```bash
# Check SSL certificate status
docker exec sdg-application /usr/local/bin/reset-setup.sh status

# Force SSL certificate recreation
docker exec sdg-application /usr/local/bin/reset-setup.sh force-config
```

### Container Issues

#### Container Won't Start

```bash
# Check container logs
docker-compose logs

# Check for port conflicts
docker ps -a
netstat -tlnp | grep -E "58090|58080"

# Clean restart
docker-compose down -v
docker-compose up -d --build
```

#### Performance Issues

```bash
# Check resource usage
docker stats sdg-application

# Check supervisor configuration
docker exec sdg-application /usr/local/bin/supervisor-gtw.sh config

# Use conservative preset for unstable environments
docker exec sdg-application /usr/local/bin/configure-supervisor.sh conservative
```

### Common Solutions

| Issue | Solution |
|-------|----------|
| Web interface not accessible | Check services: `/usr/local/bin/supervisor-gtw.sh status` |
| License errors | Check status: `/usr/local/bin/reset-setup.sh status` |
| Services keep restarting | Use conservative preset: `configure-supervisor.sh conservative` |
| Supervisor stopped | Automatic recovery active, or manual: `start-supervision.sh` |
| SSL certificate errors | Force recreation: `reset-setup.sh force-config` |
| Port conflicts | Modify `docker-compose.yml` port mappings |

## 📁 File Structure

```text
Docker/
├── docker-compose.yml              # Main compose configuration
├── Dockerfile.sdg                 # SDG application image
├── start-sdg.sh                   # Robust container startup script
├── supervisor-gtw.sh              # Advanced GTW service supervisor
├── configure-supervisor.sh        # Supervisor configuration tool
├── start-supervision.sh           # Supervision startup script
├── restart-gtw.sh                 # Manual service restart tool
├── reset-setup.sh                 # Setup state management tool
├── run-docker.sh                  # Raw Docker deployment script
├── README.md                      # This documentation
└── assets/                        # Required installation files
    ├── tmwsdg-5.2.3-22411.x86_64_U22.04.deb
    ├── aksusbd_8.53-1_amd64.deb
    ├── haspvlib_x86_64_102099.so
    ├── install_v2c_x86_64
    └── SDG_Unlocked_20190321_171527.v2c
```

## 🎯 Key Features Summary

### 🔄 Self-Healing & Reliability

- **Multi-layer supervision** with automatic service recovery
- **Supervisor watchdog** that monitors and restarts supervision
- **Intelligent restart management** with adaptive failure tracking
- **Graceful shutdown** with proper signal handling
- **Health monitoring** with configurable failure thresholds

### 💾 Persistent Storage

- **Complete persistence** for licenses, configurations, and data
- **Smart license management** preserves user-installed permanent licenses
- **SSL certificate persistence** eliminates recreation on restart
- **Workspace preservation** maintains user configurations and data

### 🛠️ Advanced Management

- **Configurable supervision** with fast/normal/conservative presets
- **Comprehensive diagnostics** with built-in status reporting
- **Zero-touch operation** with fully automated setup
- **Production-ready** enterprise-grade robustness

### 🚀 Developer Experience

- **Single command deployment** with Docker Compose
- **Comprehensive logging** with timestamped entries
- **Rich toolset** for monitoring and troubleshooting
- **Clear documentation** with practical examples

## 🔄 Migration from Previous Versions

This production-ready version includes major improvements:

### Architecture Changes

- ✅ **Persistent volumes** replace tmpfs for critical data
- ✅ **Multi-layer supervision** replaces simple process monitoring
- ✅ **Smart license management** replaces basic V2C installation
- ✅ **Self-healing capabilities** replace manual intervention requirements

### New Features Added

- ✅ **Supervisor watchdog** for automatic recovery
- ✅ **License change detection** for intelligent restart management
- ✅ **Configurable supervision** with multiple presets
- ✅ **Comprehensive diagnostics** with status reporting tools
- ✅ **Graceful shutdown** with proper cleanup

### Compatibility

- ✅ **Backward compatible** with existing deployments
- ✅ **Automatic migration** of existing data to persistent storage
- ✅ **Same API endpoints** and web interface
- ✅ **Enhanced reliability** without breaking changes

---

## 📞 Support

For issues or questions:

1. Check the troubleshooting section above
2. Use the built-in diagnostic tools
3. Review container logs for detailed information
4. Ensure all prerequisites are met

**The container is designed for zero-touch operation with comprehensive self-healing capabilities.**
