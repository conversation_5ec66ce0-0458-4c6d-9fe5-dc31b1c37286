

services:
  # Main SDG application container
  sdg:
    build:
      context: .
      dockerfile: Dockerfile.sdg
    container_name: sdg-application
    platform: linux/amd64
    hostname: sdg-sentinel
    domainname: local
    privileged: true
    cap_add:
      - SYS_ADMIN
      - SYS_PTRACE
      - MKNOD
      - SYS_RAWIO
    security_opt:
      - seccomp:unconfined
      - apparmor:unconfined
    devices:
      - /dev/urandom:/dev/urandom
      - /dev/fuse:/dev/fuse
# Option 2: RTE and licenses inside container with tmpfs storage
    ports:
      - "58090:58090"
      - "58080:58080"
      - "4885:4885"
      - "1947:1947"
      - "102-103:102-103"
      - "2404-2406:2404-2406"
      - "20000-20003:20000-20003"
      - "502-503:502-503"
    volumes:
      - /sys/fs/cgroup:/sys/fs/cgroup:rw
      - sdg_data:/opt/sdg/data
      - hasplm_data:/var/hasplm
      - hasplm_config:/etc/hasplm
      - sdg_config:/etc/tmw/sdg
    tmpfs:
      - /run
      - /run/lock
      - /tmp
    environment:
      - container=docker
      # GTW Supervisor configuration (optional - can also use config file)
      # - GTW_MAX_RESTART_ATTEMPTS=5
      # - GTW_RESTART_DELAY=5
      # - GTW_HEALTH_CHECK_INTERVAL=5
      # - GTW_LOG_DIR=/var/log
    # Use image default CMD (start-sdg.sh)
    # command: /usr/sbin/init
    # stop_signal: SIGRTMIN+3
    networks:
      - sdg-network
    restart: unless-stopped

volumes:
  sdg_data:
    driver: local
  hasplm_data:
    driver: local
  hasplm_config:
    driver: local
  sdg_config:
    driver: local

networks:
  sdg-network:
    driver: bridge
