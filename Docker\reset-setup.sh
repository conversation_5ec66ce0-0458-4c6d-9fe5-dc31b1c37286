#!/bin/bash
# Script to reset SDG setup state and force re-initialization

set -e

SETUP_STATE_FILE="/opt/sdg/data/.sdg_setup_state"
LICENSE_INSTALLED_FLAG="$SETUP_STATE_FILE.license_installed"
CONFIG_SETUP_FLAG="$SETUP_STATE_FILE.config_setup"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}SDG Setup State Manager${NC}"
echo "========================"

# Function to check if license is actually working
check_license_functional() {
  # Method 1: Check for installed Sentinel license files (most reliable)
  if [ -d "/var/hasplm/installed/102099" ]; then
    local license_files=$(find /var/hasplm/installed/102099 -type f 2>/dev/null | wc -l)
    if [ "$license_files" -gt 0 ]; then
      return 0  # Permanent license files found
    fi
  fi

  # Method 2: Check latest license log for "License found > 0"
  local latest_log=$(find /etc/tmw/sdg/LicenseLogs -name "*.log" -type f -printf '%T@ %p\n' 2>/dev/null | sort -n | tail -1 | cut -d' ' -f2-)
  if [ -n "$latest_log" ] && [ -f "$latest_log" ]; then
    local license_count=$(grep "License found=" "$latest_log" | tail -1 | sed 's/.*License found=\s*//' | tr -d ' ')
    if [ -n "$license_count" ] && [ "$license_count" -gt 0 ] 2>/dev/null; then
      return 0
    fi
  fi

  return 1
}

# Function to check if SSL certificates exist and are properly configured
check_ssl_certificates() {
  local cert_dir="/etc/tmw/sdg/ssl/certs"
  local key_dir="/etc/tmw/sdg/ssl/private"
  local config_file="/etc/tmw/sdg/gtw_config.json"

  # First check if certificate files exist
  if [ ! -d "$cert_dir" ] || [ ! -d "$key_dir" ]; then
    return 1  # Directories don't exist
  fi

  local cert_files=$(find "$cert_dir" -name "*.crt" -type f | wc -l)
  local key_files=$(find "$key_dir" -name "*.key" -type f | wc -l)

  if [ "$cert_files" -eq 0 ] || [ "$key_files" -eq 0 ]; then
    return 1  # No certificate files found
  fi

  # Check if config file exists and has certificate paths configured
  if [ -f "$config_file" ]; then
    # Check if the config file has HTTPS certificate and key paths specified
    # Look for both possible field names: httpsCertificateFile and gtwHttpsCertificateFile
    local cert_path=$(grep -o '"[^"]*[Hh]ttps[Cc]ertificate[Ff]ile"[[:space:]]*:[[:space:]]*"[^"]*"' "$config_file" 2>/dev/null | sed 's/.*"[^"]*[Hh]ttps[Cc]ertificate[Ff]ile"[[:space:]]*:[[:space:]]*"\([^"]*\)".*/\1/')
    local key_path=$(grep -o '"[^"]*[Hh]ttps[Pp]rivate[Kk]ey[Ff]ile"[[:space:]]*:[[:space:]]*"[^"]*"' "$config_file" 2>/dev/null | sed 's/.*"[^"]*[Hh]ttps[Pp]rivate[Kk]ey[Ff]ile"[[:space:]]*:[[:space:]]*"\([^"]*\)".*/\1/')

    # If paths are relative, make them absolute based on SSL directory
    if [ -n "$cert_path" ] && [[ "$cert_path" != /* ]]; then
      cert_path="$cert_dir/$cert_path"
    fi
    if [ -n "$key_path" ] && [[ "$key_path" != /* ]]; then
      key_path="$key_dir/$key_path"
    fi

    # Check if both paths are specified and files exist
    if [ -n "$cert_path" ] && [ -n "$key_path" ] && [ -f "$cert_path" ] && [ -f "$key_path" ]; then
      return 0  # Certificates exist and are properly configured
    fi
  fi

  return 1  # Certificates not properly configured
}

show_status() {
    echo "Current setup state:"
    echo -n "  License flag file: "
    if [ -f "$LICENSE_INSTALLED_FLAG" ]; then
        local flag_age=$(($(date +%s) - $(stat -c %Y "$LICENSE_INSTALLED_FLAG" 2>/dev/null || echo 0)))
        echo -e "${GREEN}YES${NC} ($(stat -c %y "$LICENSE_INSTALLED_FLAG" | cut -d. -f1), ${flag_age}s ago)"
    else
        echo -e "${RED}NO${NC}"
    fi

    echo -n "  License functional: "
    if check_license_functional; then
        echo -e "${GREEN}YES${NC}"
    else
        echo -e "${RED}NO${NC}"
    fi

    echo -n "  Sentinel license files: "
    if [ -d "/var/hasplm/installed/102099" ]; then
        local license_files=$(find /var/hasplm/installed/102099 -type f 2>/dev/null | wc -l)
        if [ "$license_files" -gt 0 ]; then
            echo -e "${GREEN}$license_files file(s) found${NC}"
            find /var/hasplm/installed/102099 -type f 2>/dev/null | head -3 | while read file; do
                echo "    $(basename "$file")"
            done
        else
            echo -e "${YELLOW}Directory exists but empty${NC}"
        fi
    else
        echo -e "${RED}No permanent license directory${NC}"
    fi

    echo -n "  V2C install policy: "
    if [ ! -f "$LICENSE_INSTALLED_FLAG" ]; then
        echo -e "${YELLOW}WILL INSTALL${NC} (first run)"
    else
        local flag_age=$(($(date +%s) - $(stat -c %Y "$LICENSE_INSTALLED_FLAG" 2>/dev/null || echo 0)))
        if [ $flag_age -gt 3600 ]; then
            echo -e "${YELLOW}WILL INSTALL${NC} (flag older than 1 hour)"
        else
            echo -e "${GREEN}WILL SKIP${NC} (preserving user license)"
        fi
    fi

    echo -n "  Config flag file: "
    if [ -f "$CONFIG_SETUP_FLAG" ]; then
        echo -e "${GREEN}YES${NC} ($(stat -c %y "$CONFIG_SETUP_FLAG" | cut -d. -f1))"
    else
        echo -e "${RED}NO${NC}"
    fi

    echo -n "  GTW config file: "
    if [ -f "/etc/tmw/sdg/gtw_config.json" ]; then
        echo -e "${GREEN}EXISTS${NC} ($(stat -c %y "/etc/tmw/sdg/gtw_config.json" | cut -d. -f1))"
    else
        echo -e "${RED}MISSING${NC}"
    fi

    echo -n "  SSL certificates: "
    if check_ssl_certificates; then
        echo -e "${GREEN}PROPERLY CONFIGURED${NC}"
        local cert_count=$(find /etc/tmw/sdg/ssl/certs -name "*.crt" -type f | wc -l)
        local key_count=$(find /etc/tmw/sdg/ssl/private -name "*.key" -type f | wc -l)
        echo "    ($cert_count certificate(s), $key_count key(s))"

        # Show configured paths from config file
        local config_file="/etc/tmw/sdg/gtw_config.json"
        if [ -f "$config_file" ]; then
            local cert_path=$(grep -o '"[^"]*[Hh]ttps[Cc]ertificate[Ff]ile"[[:space:]]*:[[:space:]]*"[^"]*"' "$config_file" 2>/dev/null | sed 's/.*"[^"]*[Hh]ttps[Cc]ertificate[Ff]ile"[[:space:]]*:[[:space:]]*"\([^"]*\)".*/\1/')
            local key_path=$(grep -o '"[^"]*[Hh]ttps[Pp]rivate[Kk]ey[Ff]ile"[[:space:]]*:[[:space:]]*"[^"]*"' "$config_file" 2>/dev/null | sed 's/.*"[^"]*[Hh]ttps[Pp]rivate[Kk]ey[Ff]ile"[[:space:]]*:[[:space:]]*"\([^"]*\)".*/\1/')
            if [ -n "$cert_path" ] && [ -n "$key_path" ]; then
                # Show absolute paths
                local cert_dir="/etc/tmw/sdg/ssl/certs"
                local key_dir="/etc/tmw/sdg/ssl/private"
                if [[ "$cert_path" != /* ]]; then
                    cert_path="$cert_dir/$cert_path"
                fi
                if [[ "$key_path" != /* ]]; then
                    key_path="$key_dir/$key_path"
                fi
                echo "    Cert: $cert_path"
                echo "    Key:  $key_path"
            fi
        fi
    else
        echo -e "${RED}MISSING OR NOT CONFIGURED${NC}"
        # Show what's missing
        local cert_count=$(find /etc/tmw/sdg/ssl/certs -name "*.crt" -type f 2>/dev/null | wc -l)
        local key_count=$(find /etc/tmw/sdg/ssl/private -name "*.key" -type f 2>/dev/null | wc -l)
        if [ "$cert_count" -eq 0 ] || [ "$key_count" -eq 0 ]; then
            echo "    Certificate files missing ($cert_count cert(s), $key_count key(s))"
        else
            echo "    Files exist but not configured in gtw_config.json"
        fi
    fi
}

reset_license() {
    echo -e "${YELLOW}Resetting license setup state...${NC}"
    rm -f "$LICENSE_INSTALLED_FLAG"
    echo "License setup state reset. Next container restart will attempt license installation."
}

reset_config() {
    echo -e "${YELLOW}Resetting config setup state...${NC}"
    rm -f "$CONFIG_SETUP_FLAG"
    echo "Config setup state reset. Next container restart will run GTWSettings."
}

reset_all() {
    echo -e "${YELLOW}Resetting all setup state...${NC}"
    rm -f "$LICENSE_INSTALLED_FLAG"
    rm -f "$CONFIG_SETUP_FLAG"
    echo "All setup state reset. Next container restart will perform full initialization."
}

force_license_reinstall() {
    echo -e "${YELLOW}Force reinstalling V2C license...${NC}"
    echo -e "${RED}WARNING: This will overwrite any user-installed license!${NC}"
    cd /home/<USER>"Cannot access /home/<USER>"; exit 1; }

    if ./install_v2c_x86_64 SDG_Unlocked_20190321_171527.v2c; then
        echo -e "${GREEN}V2C license installation successful${NC}"
        touch "$LICENSE_INSTALLED_FLAG"
    else
        echo -e "${RED}V2C license installation failed${NC}"
        touch "$LICENSE_INSTALLED_FLAG"  # Still create flag to prevent repeated attempts
    fi
}

force_config_reset() {
    echo -e "${YELLOW}Force reconfiguring SDG...${NC}"

    # Run GTWSettings
    echo "Creating SSL certificates and basic configuration..."
    /usr/bin/GTWSettings -z || echo "GTWSettings -z failed"

    echo "Setting admin password..."
    /usr/bin/GTWSettings -u "passwordA1." || echo "GTWSettings -u failed"

    echo "Setting host to localhost..."
    /usr/bin/GTWSettings -h 127.0.0.1 || echo "GTWSettings -h failed"

    # Update localhost setting
    CFG=/etc/tmw/sdg/gtw_config.json
    if [ -f "$CFG" ]; then
        sed -i -E 's/"gtwUseLocalHostForEngineAndMonitorComms"\s*:\s*false/"gtwUseLocalHostForEngineAndMonitorComms": true/g' "$CFG" || true
    fi

    # Verify configuration was successful
    if [ -f "$CFG" ] && check_ssl_certificates; then
        touch "$CONFIG_SETUP_FLAG"
        echo -e "${GREEN}Configuration completed successfully${NC}"
    else
        echo -e "${RED}Configuration may have failed${NC}"
        rm -f "$CONFIG_SETUP_FLAG"
        if [ ! -f "$CFG" ]; then
            echo "  - GTW config file not created"
        fi
        if ! check_ssl_certificates; then
            echo "  - SSL certificates not created"
        fi
    fi
}

case "${1:-status}" in
    "status")
        show_status
        ;;
    "reset-license")
        reset_license
        show_status
        ;;
    "reset-config")
        reset_config
        show_status
        ;;
    "reset-all")
        reset_all
        show_status
        ;;
    "force-license")
        force_license_reinstall
        show_status
        ;;
    "force-config")
        force_config_reset
        show_status
        ;;
    "help"|"-h"|"--help")
        echo "Usage: $0 [COMMAND]"
        echo ""
        echo "Commands:"
        echo "  status         Show current setup state (default)"
        echo "  reset-license  Reset license installation flag"
        echo "  reset-config   Reset configuration setup flag"
        echo "  reset-all      Reset all setup flags"
        echo "  force-license  Force reinstall license now"
        echo "  force-config   Force reconfigure SDG now"
        echo "  help          Show this help message"
        echo ""
        echo "Setup state files are stored in /opt/sdg/data/ (persistent volume)"
        ;;
    *)
        echo -e "${RED}Unknown command: $1${NC}"
        echo "Use '$0 help' for usage information"
        exit 1
        ;;
esac
