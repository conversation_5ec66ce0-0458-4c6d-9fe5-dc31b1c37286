#!/bin/bash
# Script to restart GTW services inside the container

set -e

echo "=== GTW Service Restart Script ==="

# Function to check if a process is running
is_running() {
    pgrep -f "$1" >/dev/null 2>&1
}

# Function to wait for process to stop
wait_for_stop() {
    local process_name="$1"
    local max_wait=10
    local count=0
    
    while is_running "$process_name" && [ $count -lt $max_wait ]; do
        echo "Waiting for $process_name to stop... ($count/$max_wait)"
        sleep 1
        count=$((count + 1))
    done
    
    if is_running "$process_name"; then
        echo "Force killing $process_name..."
        pkill -9 -f "$process_name" || true
        sleep 1
    fi
}

# Stop GTW services
echo "Stopping GTW services..."
if is_running "GTWWebMonitor"; then
    echo "Stopping GTWWebMonitor..."
    pkill -TERM -f GTWWebMonitor || true
    wait_for_stop "GTWWebMonitor"
fi

if is_running "GTWEngine"; then
    echo "Stopping GTWEngine..."
    pkill -TERM -f GTWEngine || true
    wait_for_stop "GTWEngine"
fi

echo "All GTW services stopped."

# Clear old log files
echo "Clearing old log files..."
> /var/log/gtw-monitor.log
> /var/log/gtw-engine.log

# Start GTW services
echo "Starting GTW services..."

if [ -f /usr/bin/GTWWebMonitor ]; then
    echo "Starting GTWWebMonitor..."
    nohup /usr/bin/GTWWebMonitor -service > /var/log/gtw-monitor.log 2>&1 &
    sleep 1
fi

if [ -f /usr/bin/GTWEngine ]; then
    echo "Starting GTWEngine..."
    nohup /usr/bin/GTWEngine > /var/log/gtw-engine.log 2>&1 &
    sleep 1
fi

# Wait and report status
sleep 3
echo "=== Service Status ==="
if is_running "GTWWebMonitor"; then
    echo "✓ GTWWebMonitor is running (PID: $(pgrep -f GTWWebMonitor | head -1))"
else
    echo "✗ GTWWebMonitor is not running"
fi

if is_running "GTWEngine"; then
    echo "✓ GTWEngine is running (PID: $(pgrep -f GTWEngine | head -1))"
else
    echo "✗ GTWEngine is not running"
fi

echo "=== All GTW Processes ==="
pgrep -af GTW || echo "No GTW processes found"

echo "=== Recent Log Entries ==="
echo "--- GTWWebMonitor Log ---"
tail -5 /var/log/gtw-monitor.log 2>/dev/null || echo "No monitor log entries"

echo "--- GTWEngine Log ---"
tail -5 /var/log/gtw-engine.log 2>/dev/null || echo "No engine log entries"

echo "GTW service restart completed."

# Start supervision if supervisor script is available
if [ -f /usr/local/bin/supervisor-gtw.sh ]; then
    echo "Starting GTW service supervision..."
    nohup /usr/local/bin/supervisor-gtw.sh monitor > /var/log/gtw-supervisor.log 2>&1 &
    echo "Supervision started (PID: $!)"
fi
