#!/bin/bash
# Raw Docker deployment script for SDG
# Alternative to docker-compose for environments without compose support

set -e

# Configuration
IMAGE_NAME="docker-sdg:latest"
CONTAINER_NAME="sdg-application"
VOLUME_NAME="sdg_data"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}SDG Docker Deployment Script${NC}"
echo "================================"

# Function to check if container exists
container_exists() {
    docker ps -a --format '{{.Names}}' | grep -q "^${CONTAINER_NAME}$"
}

# Function to check if container is running
container_running() {
    docker ps --format '{{.Names}}' | grep -q "^${CONTAINER_NAME}$"
}

# Function to check if image exists
image_exists() {
    docker images --format '{{.Repository}}:{{.Tag}}' | grep -q "^${IMAGE_NAME}$"
}

# Function to create volume if it doesn't exist
create_volume() {
    if ! docker volume ls --format '{{.Name}}' | grep -q "^${VOLUME_NAME}$"; then
        echo -e "${YELLOW}Creating volume: ${VOLUME_NAME}${NC}"
        docker volume create "${VOLUME_NAME}"
    else
        echo -e "${GREEN}Volume ${VOLUME_NAME} already exists${NC}"
    fi
}

# Function to build image
build_image() {
    echo -e "${YELLOW}Building image: ${IMAGE_NAME}${NC}"
    docker build -f Dockerfile.sdg -t "${IMAGE_NAME}" .
}

# Function to run container
run_container() {
    echo -e "${YELLOW}Starting container: ${CONTAINER_NAME}${NC}"
    docker run -d \
        --name "${CONTAINER_NAME}" \
        --privileged \
        --cap-add SYS_ADMIN \
        --cap-add SYS_PTRACE \
        --cap-add MKNOD \
        --cap-add SYS_RAWIO \
        --security-opt seccomp:unconfined \
        --security-opt apparmor:unconfined \
        --device /dev/urandom:/dev/urandom \
        --device /dev/fuse:/dev/fuse \
        --hostname sdg-sentinel \
        --domainname local \
        --platform linux/amd64 \
        --tmpfs /run \
        --tmpfs /run/lock \
        --tmpfs /tmp \
        --tmpfs /var/hasplm:exec \
        --tmpfs /etc/hasplm:exec \
        -v /sys/fs/cgroup:/sys/fs/cgroup:rw \
        -v "${VOLUME_NAME}":/opt/sdg/data \
        -p 58090:58090 \
        -p 58080:58080 \
        -p 4885:4885 \
        -p 1947:1947 \
        -p 102:102 \
        -p 103:103 \
        -p 2404:2404 \
        -p 2405:2405 \
        -p 2406:2406 \
        -p 20000:20000 \
        -p 20001:20001 \
        -p 20002:20002 \
        -p 20003:20003 \
        -p 502:502 \
        -p 503:503 \
        --restart unless-stopped \
        "${IMAGE_NAME}"
}

# Parse command line arguments
case "${1:-start}" in
    "build")
        build_image
        ;;
    "start")
        # Check if image exists
        if ! image_exists; then
            echo -e "${YELLOW}Image not found. Building...${NC}"
            build_image
        fi
        
        # Create volume
        create_volume
        
        # Stop existing container if running
        if container_running; then
            echo -e "${YELLOW}Stopping existing container...${NC}"
            docker stop "${CONTAINER_NAME}"
        fi
        
        # Remove existing container if exists
        if container_exists; then
            echo -e "${YELLOW}Removing existing container...${NC}"
            docker rm "${CONTAINER_NAME}"
        fi
        
        # Run new container
        run_container
        
        echo -e "${GREEN}Container started successfully!${NC}"
        echo -e "Web Interface: ${YELLOW}https://127.0.0.1:58090${NC}"
        echo -e "Login: ${YELLOW}admin / passwordA1.${NC}"
        ;;
    "stop")
        if container_running; then
            echo -e "${YELLOW}Stopping container...${NC}"
            docker stop "${CONTAINER_NAME}"
            echo -e "${GREEN}Container stopped${NC}"
        else
            echo -e "${RED}Container is not running${NC}"
        fi
        ;;
    "restart")
        $0 stop
        sleep 2
        $0 start
        ;;
    "status")
        if container_running; then
            echo -e "${GREEN}Container is running${NC}"
            docker ps --filter "name=${CONTAINER_NAME}" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
        else
            echo -e "${RED}Container is not running${NC}"
        fi
        ;;
    "logs")
        if container_exists; then
            docker logs -f "${CONTAINER_NAME}"
        else
            echo -e "${RED}Container does not exist${NC}"
        fi
        ;;
    "shell")
        if container_running; then
            docker exec -it "${CONTAINER_NAME}" bash
        else
            echo -e "${RED}Container is not running${NC}"
        fi
        ;;
    "cleanup")
        echo -e "${YELLOW}Cleaning up...${NC}"
        if container_exists; then
            docker rm -f "${CONTAINER_NAME}" 2>/dev/null || true
        fi
        if image_exists; then
            docker rmi "${IMAGE_NAME}" 2>/dev/null || true
        fi
        if docker volume ls --format '{{.Name}}' | grep -q "^${VOLUME_NAME}$"; then
            docker volume rm "${VOLUME_NAME}" 2>/dev/null || true
        fi
        echo -e "${GREEN}Cleanup complete${NC}"
        ;;
    "help"|"-h"|"--help")
        echo "Usage: $0 [COMMAND]"
        echo ""
        echo "Commands:"
        echo "  build     Build the Docker image"
        echo "  start     Start the container (default)"
        echo "  stop      Stop the container"
        echo "  restart   Restart the container"
        echo "  status    Show container status"
        echo "  logs      Show container logs"
        echo "  shell     Connect to container shell"
        echo "  cleanup   Remove container, image, and volume"
        echo "  help      Show this help message"
        ;;
    *)
        echo -e "${RED}Unknown command: $1${NC}"
        echo "Use '$0 help' for usage information"
        exit 1
        ;;
esac
