#!/bin/bash
set -e

# Enable error handling and logging
exec > >(tee -a /var/log/start-sdg.log)
exec 2>&1

# Function for logging with timestamps
log() {
    echo "$(date): $*"
}

# Function for error logging
error() {
    echo "$(date): ERROR: $*" >&2
}

# Function for warning logging
warn() {
    echo "$(date): WARNING: $*" >&2
}

log "Starting SDG container..."

# 0) Prepare directories and setup state tracking
mkdir -p /opt/sdg/data  # Ensure persistent data directory exists

# 0.1) Prepare fridge dir when using tmpfs: seed VLIB and init files
mkdir -p /var/hasplm /etc/hasplm
if [ ! -f /var/hasplm/haspvlib_x86_64_102099.so ] && [ -f /usr/local/share/hasp/haspvlib_x86_64_102099.so ]; then
  cp /usr/local/share/hasp/haspvlib_x86_64_102099.so /var/hasplm/
fi
if [ ! -d /var/hasplm/init ] && [ -d /usr/local/share/hasp/init ]; then
  cp -r /usr/local/share/hasp/init /var/hasplm/
fi

# 1) Start local Sentinel licensing (aksusbd + hasplmd)
log "Starting local Sentinel licensing (aksusbd + hasplmd)..."
if [ -x /var/hasplm/init/aksusbd.rc ]; then
  /var/hasplm/init/aksusbd.rc start || echo "aksusbd start failed"
else
  echo "Warning: /var/hasplm/init/aksusbd.rc not found"
fi

# Try to start hasplmd directly and log output
if ! pgrep -f hasplmd_x86_64 >/dev/null 2>&1; then
  if command -v /usr/sbin/hasplmd_x86_64 >/dev/null 2>&1; then
    echo "Starting hasplmd_x86_64 directly..."
    /usr/sbin/hasplmd_x86_64 -s >/var/log/hasplmd.start.log 2>&1 || echo "hasplmd start failed"
  else
    echo "Warning: /usr/sbin/hasplmd_x86_64 not found"
  fi
fi

# Wait until both are up; dump diagnostics if they are not
for i in $(seq 1 20); do
  if pgrep -f aksusbd_x86_64 >/dev/null && pgrep -f hasplmd_x86_64 >/dev/null; then
    echo "Sentinel services are running"; break
  fi
  echo "Waiting for Sentinel services... ($i)"; sleep 2
done

USE_EXTERNAL_LM=false
if ! pgrep -f hasplmd_x86_64 >/dev/null; then
  echo "hasplmd_x86_64 did not start. Diagnostics:"
  ls -la /var/hasplm || true
  [ -f /var/hasplm/error.log ] && tail -50 /var/hasplm/error.log || echo "No /var/hasplm/error.log"
  [ -f /var/hasplm/hasplmd.start.log ] && tail -50 /var/hasplm/hasplmd.start.log || true
  echo "No local hasplmd detected yet; continuing and will attempt local install."
  USE_EXTERNAL_LM=false
fi

# 2) Install license (AdminMode V2C) only if local hasplmd is running and license is working
SETUP_STATE_FILE="/opt/sdg/data/.sdg_setup_state"
LICENSE_INSTALLED_FLAG="$SETUP_STATE_FILE.license_installed"

# Function to check if license is actually working
check_license_functional() {
  # Method 1: Check for installed Sentinel license files (most reliable)
  if [ -d "/var/hasplm/installed/102099" ]; then
    local license_files=$(find /var/hasplm/installed/102099 -type f 2>/dev/null | wc -l)
    if [ "$license_files" -gt 0 ]; then
      return 0  # Permanent license files found
    fi
  fi

  # Method 2: Check latest license log for "License found > 0"
  local latest_log=$(find /etc/tmw/sdg/LicenseLogs -name "*.log" -type f -printf '%T@ %p\n' 2>/dev/null | sort -n | tail -1 | cut -d' ' -f2-)
  if [ -n "$latest_log" ] && [ -f "$latest_log" ]; then
    # Look for "License found= X" where X > 0
    local license_count=$(grep "License found=" "$latest_log" | tail -1 | sed 's/.*License found=\s*//' | tr -d ' ')
    if [ -n "$license_count" ] && [ "$license_count" -gt 0 ] 2>/dev/null; then
      return 0  # License is functional
    fi
  fi

  return 1  # License not functional
}

# Function to check if SSL certificates exist and are properly configured
check_ssl_certificates() {
  local cert_dir="/etc/tmw/sdg/ssl/certs"
  local key_dir="/etc/tmw/sdg/ssl/private"
  local config_file="/etc/tmw/sdg/gtw_config.json"

  # First check if certificate files exist
  if [ ! -d "$cert_dir" ] || [ ! -d "$key_dir" ]; then
    return 1  # Directories don't exist
  fi

  local cert_files=$(find "$cert_dir" -name "*.crt" -type f | wc -l)
  local key_files=$(find "$key_dir" -name "*.key" -type f | wc -l)

  if [ "$cert_files" -eq 0 ] || [ "$key_files" -eq 0 ]; then
    return 1  # No certificate files found
  fi

  # Check if config file exists and has certificate paths configured
  if [ -f "$config_file" ]; then
    # Check if the config file has HTTPS certificate and key paths specified
    # Look for both possible field names: httpsCertificateFile and gtwHttpsCertificateFile
    local cert_path=$(grep -o '"[^"]*[Hh]ttps[Cc]ertificate[Ff]ile"[[:space:]]*:[[:space:]]*"[^"]*"' "$config_file" 2>/dev/null | sed 's/.*"[^"]*[Hh]ttps[Cc]ertificate[Ff]ile"[[:space:]]*:[[:space:]]*"\([^"]*\)".*/\1/')
    local key_path=$(grep -o '"[^"]*[Hh]ttps[Pp]rivate[Kk]ey[Ff]ile"[[:space:]]*:[[:space:]]*"[^"]*"' "$config_file" 2>/dev/null | sed 's/.*"[^"]*[Hh]ttps[Pp]rivate[Kk]ey[Ff]ile"[[:space:]]*:[[:space:]]*"\([^"]*\)".*/\1/')

    # If paths are relative, make them absolute based on SSL directory
    if [ -n "$cert_path" ] && [[ "$cert_path" != /* ]]; then
      cert_path="$cert_dir/$cert_path"
    fi
    if [ -n "$key_path" ] && [[ "$key_path" != /* ]]; then
      key_path="$key_dir/$key_path"
    fi

    # Check if both paths are specified and files exist
    if [ -n "$cert_path" ] && [ -n "$key_path" ] && [ -f "$cert_path" ] && [ -f "$key_path" ]; then
      return 0  # Certificates exist and are properly configured
    fi
  fi

  return 1  # Certificates not properly configured
}

cd /home/<USER>
chmod +x ./install_v2c_x86_64 2>/dev/null || true

if [ "$USE_EXTERNAL_LM" = "false" ]; then
  # Check if we have a functional license
  if check_license_functional; then
    echo "Functional license detected (skipping V2C installation)"
  else
    # Check if this is the first run or if we should retry V2C installation
    # On fresh container, we need to be more aggressive about installing V2C
    should_install_v2c=false

    if [ ! -f "$LICENSE_INSTALLED_FLAG" ]; then
      echo "First run detected - will attempt V2C installation"
      should_install_v2c=true
    else
      # Check if flag file is older than 1 hour (3600 seconds)
      flag_age=$(($(date +%s) - $(stat -c %Y "$LICENSE_INSTALLED_FLAG" 2>/dev/null || echo 0)))
      if [ $flag_age -gt 3600 ]; then
        echo "License flag is old ($flag_age seconds) - will retry V2C installation"
        should_install_v2c=true
      else
        # Check if we actually have any license files - if not, install V2C regardless of flag age
        if [ ! -d "/var/hasplm/installed/102099" ] || [ "$(find /var/hasplm/installed/102099 -type f 2>/dev/null | wc -l)" -eq 0 ]; then
          echo "No license files found despite recent flag ($flag_age seconds ago)"
          echo "This may be a fresh container - attempting V2C installation"
          should_install_v2c=true
        else
          echo "Recent V2C installation attempt detected ($flag_age seconds ago)"
          echo "Skipping V2C installation to preserve any user-installed license"
          echo "Services will attempt to use available licensing"
        fi
      fi
    fi

    if [ "$should_install_v2c" = "true" ]; then
      echo "Attempting V2C installation..."

      # Wait for license manager to be ready before installing V2C
      echo "Ensuring license manager is ready for V2C installation..."
      lm_attempts=0
      lm_max_attempts=30
      while [ $lm_attempts -lt $lm_max_attempts ]; do
        if pgrep hasplmd >/dev/null && pgrep aksusbd >/dev/null; then
          echo "License manager processes are running"
          sleep 3  # Give them a moment to fully initialize
          break
        fi
        echo "Waiting for license manager processes (attempt $((lm_attempts + 1))/$lm_max_attempts)..."
        sleep 2
        lm_attempts=$((lm_attempts + 1))
      done

      if ./install_v2c_x86_64 SDG_Unlocked_20190321_171527.v2c; then
        echo "V2C installation command completed"
        # Always create flag to prevent repeated attempts
        touch "$LICENSE_INSTALLED_FLAG"
        # Wait longer for license to be processed on fresh container
        echo "Waiting for license to be processed..."
        sleep 10
        if check_license_functional; then
          echo "V2C license is now functional"
        else
          echo "V2C installation completed but license not functional yet"
          echo "This may be normal for container licensing - services will attempt to use available licensing"
        fi
      else
        echo "V2C installation command failed"
        # Still create flag to prevent repeated failed attempts
        touch "$LICENSE_INSTALLED_FLAG"
      fi
    fi
  fi
else
  echo "Skipping local V2C install (using external LM)"
fi

# 3) Configure SDG (check actual files rather than just flags)
CONFIG_SETUP_FLAG="$SETUP_STATE_FILE.config_setup"
CFG=/etc/tmw/sdg/gtw_config.json

# Check if we need to run configuration based on actual file existence
need_config=false

# Check if config file exists
if [ ! -f "$CFG" ]; then
  echo "GTW config file missing - configuration needed"
  need_config=true
fi

# Check if SSL certificates exist
if ! check_ssl_certificates; then
  echo "SSL certificates missing - configuration needed"
  need_config=true
fi

if [ "$need_config" = "true" ]; then
  echo "Configuring SDG (missing config file or SSL certificates)..."

  # Create SSL certificates and basic config
  echo "Creating SSL certificates and basic configuration..."
  /usr/bin/GTWSettings -z || echo "GTWSettings -z failed"

  # Set admin password
  echo "Setting admin password..."
  /usr/bin/GTWSettings -u "passwordA1." || echo "GTWSettings -u failed"

  # Set host to localhost
  echo "Setting host to localhost..."
  /usr/bin/GTWSettings -h 127.0.0.1 || echo "GTWSettings -h failed"

  # Verify configuration was successful
  if [ -f "$CFG" ] && check_ssl_certificates; then
    touch "$CONFIG_SETUP_FLAG"
    echo "SDG configuration completed successfully"
  else
    echo "SDG configuration may have failed - will retry next time"
    rm -f "$CONFIG_SETUP_FLAG"
  fi
else
  echo "SDG already configured (config file and SSL certificates exist)"
fi

# Always ensure localhost communication (this is safe to run multiple times)
if [ -f "$CFG" ]; then
  sed -i -E 's/"gtwUseLocalHostForEngineAndMonitorComms"\s*:\s*false/"gtwUseLocalHostForEngineAndMonitorComms": true/g' "$CFG" || true
fi

# 4) Start GTW services with auto-restart supervision

# Function to wait for license manager to be ready
wait_for_license_ready() {
  local max_attempts=60  # Increased for fresh containers
  local attempt=1

  echo "Waiting for license manager to be ready for GTW services..."
  while [ $attempt -le $max_attempts ]; do
    # Check if we can access license information
    if timeout 5 /usr/bin/GTWSettings -v >/dev/null 2>&1; then
      echo "License manager is ready (attempt $attempt)"
      return 0
    fi

    # Alternative check: if we have license files and license manager processes are running
    if [ -d "/var/hasplm/installed/102099" ] && [ "$(find /var/hasplm/installed/102099 -type f 2>/dev/null | wc -l)" -gt 0 ]; then
      if pgrep hasplmd >/dev/null && pgrep aksusbd >/dev/null; then
        echo "License files and processes detected, testing license access..."
        # Give it a few more seconds and try again
        sleep 3
        if timeout 5 /usr/bin/GTWSettings -v >/dev/null 2>&1; then
          echo "License manager is ready (attempt $attempt)"
          return 0
        fi
      fi
    fi

    echo "License manager not ready yet (attempt $attempt/$max_attempts), waiting..."
    sleep 3  # Increased wait time
    attempt=$((attempt + 1))
  done

  echo "Warning: License manager may not be fully ready, but proceeding with service startup"
  return 1
}

echo "Starting GTW services with auto-restart supervision..."

# Wait for license manager to be ready before starting GTW services
wait_for_license_ready

# Function to start and monitor a service
start_supervised_service() {
  local service_name="$1"
  local service_binary="$2"
  local log_file="$3"

  (
    local restart_count=0
    local max_restarts=10

    while [ $restart_count -lt $max_restarts ]; do
      echo "$(date): Starting $service_name (attempt $((restart_count + 1))/$max_restarts)..." >> "$log_file"
      $service_binary >> "$log_file" 2>&1
      exit_code=$?
      restart_count=$((restart_count + 1))

      if [ $exit_code -eq 0 ]; then
        echo "$(date): $service_name exited normally" >> "$log_file"
      else
        echo "$(date): $service_name exited with code $exit_code" >> "$log_file"
      fi

      if [ $restart_count -lt $max_restarts ]; then
        echo "$(date): Restarting $service_name in 15 seconds (attempt $((restart_count + 1))/$max_restarts)..." >> "$log_file"
        sleep 15
      else
        echo "$(date): $service_name reached maximum restart attempts ($max_restarts), stopping supervision" >> "$log_file"
        break
      fi
    done
  ) &

  echo "Started supervised $service_name (supervisor PID: $!)"
}

# Start GTW services with external supervisor (more robust than built-in supervision)
echo "Starting external GTW supervisor..."
if /usr/local/bin/start-supervision.sh; then
  echo "External supervisor started successfully"

  # Start a background process to monitor the supervisor itself
  (
    while true; do
      sleep 300  # Check every 5 minutes
      if ! pgrep -f "supervisor-gtw.sh monitor" >/dev/null; then
        echo "$(date): External supervisor stopped, attempting restart..." >> /var/log/supervisor-watchdog.log
        if /usr/local/bin/start-supervision.sh >> /var/log/supervisor-watchdog.log 2>&1; then
          echo "$(date): External supervisor restarted successfully" >> /var/log/supervisor-watchdog.log
        else
          echo "$(date): Failed to restart external supervisor" >> /var/log/supervisor-watchdog.log
        fi
      fi
    done
  ) &
  echo "Supervisor watchdog started (PID: $!)"
else
  echo "External supervisor failed to start, falling back to built-in supervision..."
  # Fallback to built-in supervision if external supervisor fails
  if [ -f /usr/bin/GTWWebMonitor ]; then
    start_supervised_service "GTWWebMonitor" "/usr/bin/GTWWebMonitor -service" "/var/log/gtw-monitor.log"
  fi

  if [ -f /usr/bin/GTWEngine ]; then
    start_supervised_service "GTWEngine" "/usr/bin/GTWEngine" "/var/log/gtw-engine.log"
  fi
fi

# 5) Setup signal handlers for graceful shutdown
cleanup() {
    echo "$(date): Received shutdown signal, cleaning up..."

    # Stop GTW services gracefully
    echo "Stopping GTW services..."
    pkill -f "GTWWebMonitor" 2>/dev/null || true
    pkill -f "GTWEngine" 2>/dev/null || true

    # Stop supervisor
    echo "Stopping supervisor..."
    pkill -f "supervisor-gtw.sh" 2>/dev/null || true

    # Stop license manager
    echo "Stopping license manager..."
    pkill -f "hasplmd" 2>/dev/null || true
    pkill -f "aksusbd" 2>/dev/null || true

    echo "Cleanup completed"
    exit 0
}

# Trap signals for graceful shutdown
trap cleanup SIGTERM SIGINT SIGQUIT

# Function to check critical service health
check_critical_services() {
    local issues=0

    # Check license manager
    if ! pgrep hasplmd >/dev/null || ! pgrep aksusbd >/dev/null; then
        echo "$(date): WARNING: License manager not running"
        issues=$((issues + 1))
    fi

    # Check external supervisor
    if ! pgrep -f "supervisor-gtw.sh monitor" >/dev/null; then
        echo "$(date): WARNING: External supervisor not running"
        issues=$((issues + 1))
    fi

    return $issues
}

# Function to attempt recovery of critical services
recover_critical_services() {
    echo "$(date): Attempting recovery of critical services..."

    # Restart license manager if needed
    if ! pgrep hasplmd >/dev/null || ! pgrep aksusbd >/dev/null; then
        echo "$(date): Restarting license manager..."
        pkill -f hasplmd 2>/dev/null || true
        pkill -f aksusbd 2>/dev/null || true
        sleep 2

        if [ -x /var/hasplm/init/aksusbd.rc ]; then
            /var/hasplm/init/aksusbd.rc start || echo "aksusbd restart failed"
        fi
        if [ -x /var/hasplm/init/hasplmd.rc ]; then
            /var/hasplm/init/hasplmd.rc start || echo "hasplmd restart failed"
        fi
        sleep 3
    fi

    # Restart external supervisor if needed
    if ! pgrep -f "supervisor-gtw.sh monitor" >/dev/null; then
        echo "$(date): Restarting external supervisor..."
        /usr/local/bin/start-supervision.sh || echo "Failed to restart supervisor"
    fi
}

# Report initial status
sleep 5
echo "$(date): SDG startup sequence completed"
echo "Initial service status:"
pgrep -af aksusbd || echo "  aksusbd: not running"
pgrep -af hasplmd || echo "  hasplmd: not running"
pgrep -af GTW || echo "  GTW services: not running"
pgrep -af "supervisor-gtw.sh" || echo "  supervisor: not running"

echo ""
echo "GTW services are running under external supervision and will auto-restart if they exit"
echo "External supervisor uses configurable settings (default: 9s health checks, 9s restart delay)"
echo "To check supervisor status: docker exec sdg-application /usr/local/bin/supervisor-gtw.sh status"
echo "To configure supervisor: docker exec sdg-application /usr/local/bin/configure-supervisor.sh"
echo "To manually restart GTW services: docker exec sdg-application /usr/local/bin/restart-gtw.sh"
echo ""

# Main monitoring loop - keep container alive and monitor critical services
echo "$(date): Starting main monitoring loop..."
health_check_interval=300  # Check every 5 minutes
consecutive_failures=0
max_consecutive_failures=3

while true; do
    sleep $health_check_interval

    if check_critical_services; then
        # All services healthy
        consecutive_failures=0
    else
        consecutive_failures=$((consecutive_failures + 1))
        echo "$(date): Health check failed ($consecutive_failures/$max_consecutive_failures)"

        if [ $consecutive_failures -ge $max_consecutive_failures ]; then
            echo "$(date): Multiple consecutive failures detected, attempting recovery..."
            recover_critical_services
            consecutive_failures=0  # Reset counter after recovery attempt
            sleep 30  # Give services time to stabilize
        fi
    fi
done

