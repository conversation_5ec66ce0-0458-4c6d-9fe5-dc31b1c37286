#!/bin/bash
# Script to start GTW service supervision

set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${GREEN}Starting GTW Service Supervision${NC}"
echo "=================================="

# Check if supervisor is already running
if pgrep -f "supervisor-gtw.sh monitor" >/dev/null; then
    echo -e "${YELLOW}Supervision is already running${NC}"
    echo "Current supervisor processes:"
    pgrep -af "supervisor-gtw.sh monitor"
    exit 0
fi

# Kill any existing GTW processes that aren't supervised
echo "Stopping any unsupervised GTW processes..."
pkill -f GTWWebMonitor || true
pkill -f GTWEngine || true
sleep 2

# Start the supervisor
echo "Starting GTW service supervisor..."
nohup /usr/local/bin/supervisor-gtw.sh monitor > /var/log/gtw-supervisor.log 2>&1 &
SUPERVISOR_PID=$!

# Wait a moment for it to start
sleep 3

# Check if it's running
if kill -0 $SUPERVISOR_PID 2>/dev/null; then
    echo -e "${GREEN}Supervision started successfully (PID: $SUPERVISOR_PID)${NC}"
    
    # Show status
    sleep 2
    /usr/local/bin/supervisor-gtw.sh status
    
    echo ""
    echo "Supervision is now active. Services will auto-restart if they exit."
    echo "To check status: docker exec sdg-application /usr/local/bin/supervisor-gtw.sh status"
    echo "To stop supervision: docker exec sdg-application pkill -f 'supervisor-gtw.sh monitor'"
else
    echo -e "${RED}Failed to start supervision${NC}"
    exit 1
fi
