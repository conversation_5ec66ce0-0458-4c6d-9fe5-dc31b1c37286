#!/bin/bash
# Advanced GTW service supervisor with configurable restart policies

set -e

# Configuration - can be overridden by environment variables or config file
CONFIG_FILE="/opt/sdg/data/supervisor.conf"

# Default values
DEFAULT_MAX_RESTART_ATTEMPTS=15
DEFAULT_RESTART_DELAY=15
DEFAULT_HEALTH_CHECK_INTERVAL=15
DEFAULT_LOG_DIR="/var/log"

# Load configuration from file if it exists
if [ -f "$CONFIG_FILE" ]; then
    echo "Loading configuration from $CONFIG_FILE"
    source "$CONFIG_FILE"
fi

# Set configuration values (environment variables take precedence)
MAX_RESTART_ATTEMPTS=${GTW_MAX_RESTART_ATTEMPTS:-${MAX_RESTART_ATTEMPTS:-$DEFAULT_MAX_RESTART_ATTEMPTS}}
RESTART_DELAY=${GTW_RESTART_DELAY:-${RESTART_DELAY:-$DEFAULT_RESTART_DELAY}}
HEALTH_CHECK_INTERVAL=${GTW_HEALTH_CHECK_INTERVAL:-${HEALTH_CHECK_INTERVAL:-$DEFAULT_HEALTH_CHECK_INTERVAL}}
LOG_DIR=${GTW_LOG_DIR:-${LOG_DIR:-$DEFAULT_LOG_DIR}}

# Service definitions
declare -A SERVICES
SERVICES[GTWWebMonitor]="/usr/bin/GTWWebMonitor -service"
SERVICES[GTWEngine]="/usr/bin/GTWEngine"

# Restart counters
declare -A RESTART_COUNTS
declare -A LAST_RESTART_TIME

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1"
}

error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1"
}

# Function to check if a service is running
is_service_running() {
    local service_name="$1"
    pgrep -f "${SERVICES[$service_name]}" >/dev/null 2>&1
}

# Function to start a service
start_service() {
    local service_name="$1"
    local service_cmd="${SERVICES[$service_name]}"
    local log_file="$LOG_DIR/gtw-${service_name,,}.log"
    
    log "Starting $service_name..."
    
    # Start service in background
    nohup $service_cmd >> "$log_file" 2>&1 &
    local pid=$!
    
    # Wait a moment and check if it's still running
    sleep 2
    if kill -0 $pid 2>/dev/null; then
        log "$service_name started successfully (PID: $pid)"
        return 0
    else
        error "$service_name failed to start"
        return 1
    fi
}

# Function to stop a service
stop_service() {
    local service_name="$1"
    
    if is_service_running "$service_name"; then
        log "Stopping $service_name..."
        pkill -TERM -f "${SERVICES[$service_name]}" || true
        
        # Wait for graceful shutdown
        local count=0
        while is_service_running "$service_name" && [ $count -lt 10 ]; do
            sleep 1
            count=$((count + 1))
        done
        
        # Force kill if still running
        if is_service_running "$service_name"; then
            warn "Force killing $service_name..."
            pkill -9 -f "${SERVICES[$service_name]}" || true
        fi
        
        log "$service_name stopped"
    fi
}

# Function to restart a service with backoff
restart_service() {
    local service_name="$1"
    local current_time=$(date +%s)
    
    # Initialize restart count if not set
    if [ -z "${RESTART_COUNTS[$service_name]}" ]; then
        RESTART_COUNTS[$service_name]=0
    fi
    
    # Reset restart count if enough time has passed (1 hour)
    if [ -n "${LAST_RESTART_TIME[$service_name]}" ]; then
        local time_diff=$((current_time - LAST_RESTART_TIME[$service_name]))
        if [ $time_diff -gt 3600 ]; then
            RESTART_COUNTS[$service_name]=0
        fi
    fi
    
    # Check if we've exceeded max restart attempts
    if [ ${RESTART_COUNTS[$service_name]} -ge $MAX_RESTART_ATTEMPTS ]; then
        error "$service_name has failed $MAX_RESTART_ATTEMPTS times. Will retry if license changes."
        return 1
    fi
    
    # Increment restart count
    RESTART_COUNTS[$service_name]=$((RESTART_COUNTS[$service_name] + 1))
    LAST_RESTART_TIME[$service_name]=$current_time
    
    warn "$service_name restart attempt ${RESTART_COUNTS[$service_name]}/$MAX_RESTART_ATTEMPTS"
    
    # Stop service if running
    stop_service "$service_name"
    
    # Wait before restart
    log "Waiting $RESTART_DELAY seconds before restarting $service_name..."
    sleep $RESTART_DELAY
    
    # Start service
    if start_service "$service_name"; then
        log "$service_name restarted successfully"
        return 0
    else
        error "$service_name restart failed"
        return 1
    fi
}

# Function to check if license has changed (to reset restart counts)
check_license_change() {
    local current_license_files=$(find /var/hasplm/installed/102099 -type f 2>/dev/null | wc -l)
    local license_file="/tmp/supervisor_license_count"
    local last_count=0

    if [ -f "$license_file" ]; then
        last_count=$(cat "$license_file")
    fi

    if [ "$current_license_files" != "$last_count" ]; then
        echo "$current_license_files" > "$license_file"
        return 0  # License changed
    fi

    return 1  # No change
}

# Function to reset restart counts (when license changes)
reset_restart_counts() {
    log "License change detected - resetting restart counts for all services"
    for service_name in "${!SERVICES[@]}"; do
        RESTART_COUNTS[$service_name]=0
        unset LAST_RESTART_TIME[$service_name]
    done
}

# Function to monitor services
monitor_services() {
    log "Starting GTW service monitor..."

    while true; do
        # Check if license has changed and reset counts if needed
        if check_license_change; then
            reset_restart_counts
        fi

        for service_name in "${!SERVICES[@]}"; do
            if ! is_service_running "$service_name"; then
                warn "$service_name is not running. Attempting restart..."
                if ! restart_service "$service_name"; then
                    # Service failed max attempts, but continue monitoring other services
                    warn "$service_name has exceeded max restart attempts. Will retry if license changes."
                fi
            fi
        done

        sleep $HEALTH_CHECK_INTERVAL
    done
}

# Function to show configuration
show_config() {
    echo "=== GTW Supervisor Configuration ==="
    echo "Max restart attempts: $MAX_RESTART_ATTEMPTS"
    echo "Restart delay: ${RESTART_DELAY}s"
    echo "Health check interval: ${HEALTH_CHECK_INTERVAL}s"
    echo "Log directory: $LOG_DIR"
    echo "Config file: $CONFIG_FILE"
    echo ""
    echo "Environment variables (override config file):"
    echo "  GTW_MAX_RESTART_ATTEMPTS=${GTW_MAX_RESTART_ATTEMPTS:-not set}"
    echo "  GTW_RESTART_DELAY=${GTW_RESTART_DELAY:-not set}"
    echo "  GTW_HEALTH_CHECK_INTERVAL=${GTW_HEALTH_CHECK_INTERVAL:-not set}"
    echo "  GTW_LOG_DIR=${GTW_LOG_DIR:-not set}"
}

# Function to show service status
show_status() {
    echo "=== GTW Service Status ==="
    for service_name in "${!SERVICES[@]}"; do
        if is_service_running "$service_name"; then
            local pid=$(pgrep -f "${SERVICES[$service_name]}" | head -1)
            echo -e "✓ $service_name: ${GREEN}Running${NC} (PID: $pid, Restarts: ${RESTART_COUNTS[$service_name]:-0})"
        else
            echo -e "✗ $service_name: ${RED}Stopped${NC} (Restarts: ${RESTART_COUNTS[$service_name]:-0})"
        fi
    done
}

# Signal handlers
cleanup() {
    log "Shutting down GTW supervisor..."
    for service_name in "${!SERVICES[@]}"; do
        stop_service "$service_name"
    done
    exit 0
}

trap cleanup SIGTERM SIGINT

# Main execution
case "${1:-monitor}" in
    "start")
        log "Starting all GTW services..."
        for service_name in "${!SERVICES[@]}"; do
            start_service "$service_name"
        done
        show_status
        ;;
    "stop")
        log "Stopping all GTW services..."
        for service_name in "${!SERVICES[@]}"; do
            stop_service "$service_name"
        done
        ;;
    "restart")
        log "Restarting all GTW services..."
        for service_name in "${!SERVICES[@]}"; do
            restart_service "$service_name"
        done
        show_status
        ;;
    "status")
        show_status
        ;;
    "config")
        show_config
        ;;
    "monitor")
        # Start services if not running
        for service_name in "${!SERVICES[@]}"; do
            if ! is_service_running "$service_name"; then
                start_service "$service_name"
            fi
        done
        
        show_status
        monitor_services
        ;;
    *)
        echo "Usage: $0 {start|stop|restart|status|config|monitor}"
        echo "  start   - Start all GTW services"
        echo "  stop    - Stop all GTW services"
        echo "  restart - Restart all GTW services"
        echo "  status  - Show service status"
        echo "  config  - Show configuration settings"
        echo "  monitor - Start monitoring mode (default)"
        echo ""
        echo "Configuration:"
        echo "  Config file: $CONFIG_FILE"
        echo "  Environment variables: GTW_MAX_RESTART_ATTEMPTS, GTW_RESTART_DELAY,"
        echo "                        GTW_HEALTH_CHECK_INTERVAL, GTW_LOG_DIR"
        exit 1
        ;;
esac
